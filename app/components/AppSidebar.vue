<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

const navigationItems = ref<NavigationMenuItem[]>([
    {
        label: 'Dashboard',
        icon: 'i-lucide-layout-dashboard',
        to: '/',
    },
    {
        label: 'Merge Block',
        icon: 'i-lucide-git-pull-request-closed',
        to: '/merge-block',
        children: [
            {
                label: 'Introduction',
                description: 'Fully styled and customizable components for Nuxt.',
                icon: 'i-lucide-house',
            },
            {
                label: 'Installation',
                description: 'Learn how to install and configure Nuxt UI in your application.',
                icon: 'i-lucide-cloud-download',
            },
            {
                label: 'Icons',
                icon: 'i-lucide-smile',
                description: 'You have nothing to do, @nuxt/icon will handle it automatically.',
            },
            {
                label: 'Colors',
                icon: 'i-lucide-swatch-book',
                description: 'Choose a primary and a neutral color from your Tailwind CSS theme.',
            },
            {
                label: 'Theme',
                icon: 'i-lucide-cog',
                description: 'You can customize components by using the `class` / `ui` props or in your app.config.ts.',
            },
        ],
    },
    {
        label: 'Audit Logs',
        icon: 'i-lucide-logs',
        to: '/audit-logs',
    },
]);

const isDesktop = ref(false);
const isSidebarOpen = ref(false);
const isCollapsed = computed(() => !isSidebarOpen.value);

const initializeSidebar = () => {
    if (!import.meta.client) return;

    const mediaQuery = window.matchMedia('(min-width: 1024px)'); // lg breakpoint
    isDesktop.value = mediaQuery.matches;
    isSidebarOpen.value = isDesktop.value;

    const handleScreenChange = (e: MediaQueryListEvent) => {
        isDesktop.value = e.matches;
        isSidebarOpen.value = isDesktop.value;
    };

    mediaQuery.addEventListener('change', handleScreenChange);

    return () => {
        mediaQuery.removeEventListener('change', handleScreenChange);
    };
};

const router = useRouter();
const closeSidebarOnNavigation = () => {
    if (!isDesktop.value) {
        isSidebarOpen.value = false;
    }
};

const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
};

onMounted(() => {
    initializeSidebar();
    router.afterEach(closeSidebarOnNavigation);
});

defineExpose({
    toggleSidebar,
    isSidebarOpen: readonly(isSidebarOpen),
    isDesktop: readonly(isDesktop),
});
</script>

<template>
    <div>
        <div
            :class="[
                'transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800',
                isDesktop ? 'w-64' : (isSidebarOpen ? 'w-64' : 'w-0'),
                'lg:relative absolute z-10 h-full',
            ]"
        >
            <div
                v-if="isDesktop || isSidebarOpen"
                class="flex flex-col h-[calc(100vh-4rem)]"
            >
                <UNavigationMenu
                    orientation="vertical"
                    highlight
                    class="h-full py-2 px-2"
                    color="primary"
                    :items="navigationItems"
                    :collapsed="isCollapsed"
                    :ui="{
                        item: '[&>*]:h-12',
                    }"
                />
            </div>
        </div>

        <div
            v-if="isSidebarOpen && !isDesktop"
            class="fixed inset-0 bg-[rgba(0,0,0,0.5)] z-0 lg:hidden"
            @click="isSidebarOpen = false"
        />
    </div>
</template>

<style lang="scss">
[data-active] {
    background-color: var(--color-blue-100);
}

.dark {
    [data-active] {
        background-color: oklch(0.278 0.033 256.848);
    }
}
</style>
