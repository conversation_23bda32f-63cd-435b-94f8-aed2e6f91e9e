<script setup lang="ts">
const sidebarRef = ref();
</script>

<template>
    <div class="flex flex-col h-screen overflow-hidden">
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    color="neutral"
                    variant="ghost"
                    size="sm"
                    square
                    class="lg:hidden"
                    @click="sidebarRef?.toggleSidebar()"
                />
                <UIcon
                    name="i-custom-sensehawk-logo"
                    class="h-8 w-8 cursor-pointer"
                    @click="$router.push('/')"
                />
            </div>
            <div class="flex items-center gap-2">
                <AppUserMenu />
            </div>
        </div>

        <div class="flex flex-1 overflow-hidden">
            <AppSidebar ref="sidebarRef" />

            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>
    </div>
</template>
